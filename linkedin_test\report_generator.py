#!/usr/bin/env python3
"""
Report Generator for LinkedIn Test
Generates reports showing companies and their LinkedIn profiles for outsourcing opportunities
"""

import sys
import os
import logging
import yaml
import json
import csv
from datetime import datetime
from typing import List, Dict, Optional

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from heroku_config import load_heroku_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class ReportGenerator:
    def __init__(self, config_path: str = "linkedin_test/test_config.yaml"):
        """Initialize the report generator"""
        self.main_config = load_heroku_config()
        self.test_config = self.load_test_config(config_path)
        self.db_manager = DatabaseManager(self.main_config)
        
    def load_test_config(self, config_path: str) -> Dict:
        """Load test configuration"""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logging.error(f"Test config file not found: {config_path}")
            return {}
        except Exception as e:
            logging.error(f"Error loading test config: {e}")
            return {}
    
    def get_companies_with_profiles(self) -> List[Dict]:
        """Get companies and their LinkedIn profiles"""
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            query = """
            SELECT 
                c.id,
                c.company_name,
                c.city,
                c.unique_positions,
                c.total_jobs,
                c.source_sites,
                c.sample_job_titles,
                c.industry_sector,
                c.company_size_estimate,
                c.outsourcing_potential_score,
                c.research_status,
                c.created_at,
                COUNT(p.id) as profiles_found,
                COUNT(CASE WHEN p.contact_priority = 'high' THEN 1 END) as high_priority_profiles,
                COUNT(CASE WHEN p.contact_priority = 'medium' THEN 1 END) as medium_priority_profiles,
                COUNT(CASE WHEN p.contact_priority = 'low' THEN 1 END) as low_priority_profiles,
                AVG(p.relevance_score) as avg_relevance_score
            FROM test_companies c
            LEFT JOIN linkedin_profiles p ON c.id = p.company_id
            GROUP BY c.id
            ORDER BY c.outsourcing_potential_score DESC, profiles_found DESC
            """
            
            cursor.execute(query)
            companies = cursor.fetchall()
            cursor.close()
            
            return companies
            
        except Exception as e:
            logging.error(f"Error getting companies with profiles: {e}")
            return []
    
    def get_profiles_for_company(self, company_id: int) -> List[Dict]:
        """Get LinkedIn profiles for a specific company"""
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            query = """
            SELECT 
                profile_name,
                profile_title,
                profile_url,
                profile_location,
                relevance_score,
                contact_priority,
                search_query,
                created_at
            FROM linkedin_profiles
            WHERE company_id = %s
            ORDER BY relevance_score DESC, contact_priority DESC
            """
            
            cursor.execute(query, (company_id,))
            profiles = cursor.fetchall()
            cursor.close()
            
            return profiles
            
        except Exception as e:
            logging.error(f"Error getting profiles for company {company_id}: {e}")
            return []
    
    def generate_text_report(self) -> str:
        """Generate a text-based report"""
        companies = self.get_companies_with_profiles()
        
        if not companies:
            return "No companies found in the test database."
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("LINKEDIN OUTSOURCING PROSPECTS REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Total Companies: {len(companies)}")
        
        total_profiles = sum(company.get('profiles_found', 0) for company in companies)
        report_lines.append(f"Total LinkedIn Profiles Found: {total_profiles}")
        report_lines.append("")
        
        # Summary statistics
        completed_companies = [c for c in companies if c['research_status'] == 'completed']
        pending_companies = [c for c in companies if c['research_status'] == 'pending']
        failed_companies = [c for c in companies if c['research_status'] == 'failed']
        
        report_lines.append("RESEARCH STATUS SUMMARY:")
        report_lines.append(f"  ✅ Completed: {len(completed_companies)} companies")
        report_lines.append(f"  ⏳ Pending: {len(pending_companies)} companies")
        report_lines.append(f"  ❌ Failed: {len(failed_companies)} companies")
        report_lines.append("")
        
        # Priority breakdown
        total_high = sum(company.get('high_priority_profiles', 0) for company in companies)
        total_medium = sum(company.get('medium_priority_profiles', 0) for company in companies)
        total_low = sum(company.get('low_priority_profiles', 0) for company in companies)
        
        report_lines.append("CONTACT PRIORITY BREAKDOWN:")
        report_lines.append(f"  🔥 High Priority: {total_high} profiles")
        report_lines.append(f"  🔶 Medium Priority: {total_medium} profiles")
        report_lines.append(f"  🔸 Low Priority: {total_low} profiles")
        report_lines.append("")
        
        # Detailed company information
        report_lines.append("DETAILED COMPANY ANALYSIS:")
        report_lines.append("-" * 80)
        
        for i, company in enumerate(companies, 1):
            report_lines.append(f"\n{i}. {company['company_name']} ({company['city']})")
            report_lines.append(f"   Industry: {company.get('industry_sector', 'Unknown')}")
            report_lines.append(f"   Size: {company.get('company_size_estimate', 'Unknown')}")
            report_lines.append(f"   Positions: {company['unique_positions']} unique, {company['total_jobs']} total")
            report_lines.append(f"   Outsourcing Score: {company.get('outsourcing_potential_score', 0)}/100")
            report_lines.append(f"   Research Status: {company['research_status'].title()}")
            
            profiles_found = company.get('profiles_found', 0)
            if profiles_found > 0:
                report_lines.append(f"   LinkedIn Profiles: {profiles_found} found")
                report_lines.append(f"   Priority Breakdown: {company.get('high_priority_profiles', 0)} high, "
                                  f"{company.get('medium_priority_profiles', 0)} medium, "
                                  f"{company.get('low_priority_profiles', 0)} low")
                
                avg_score = company.get('avg_relevance_score')
                if avg_score:
                    report_lines.append(f"   Avg Relevance Score: {avg_score:.1f}/100")
                
                # Get top profiles for this company
                profiles = self.get_profiles_for_company(company['id'])
                if profiles:
                    report_lines.append("   Top Profiles:")
                    for profile in profiles[:3]:  # Show top 3 profiles
                        priority_emoji = {"high": "🔥", "medium": "🔶", "low": "🔸"}.get(profile['contact_priority'], "")
                        report_lines.append(f"     {priority_emoji} {profile['profile_name']} - {profile.get('profile_title', 'N/A')}")
                        report_lines.append(f"       Score: {profile['relevance_score']}/100 | {profile['profile_url']}")
            else:
                report_lines.append("   LinkedIn Profiles: None found")
            
            # Sample job titles
            sample_titles = json.loads(company.get('sample_job_titles', '[]'))
            if sample_titles:
                report_lines.append(f"   Sample Positions: {', '.join(sample_titles[:3])}")
        
        return "\n".join(report_lines)
    
    def export_to_csv(self, filename: str = None) -> str:
        """Export data to CSV format"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"linkedin_test/linkedin_prospects_{timestamp}.csv"
        
        companies = self.get_companies_with_profiles()
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                # Company data CSV
                company_writer = csv.writer(csvfile)
                company_writer.writerow([
                    'Company Name', 'City', 'Industry', 'Company Size', 'Unique Positions',
                    'Total Jobs', 'Outsourcing Score', 'Research Status', 'Profiles Found',
                    'High Priority', 'Medium Priority', 'Low Priority', 'Avg Relevance Score'
                ])
                
                for company in companies:
                    company_writer.writerow([
                        company['company_name'],
                        company['city'],
                        company.get('industry_sector', ''),
                        company.get('company_size_estimate', ''),
                        company['unique_positions'],
                        company['total_jobs'],
                        company.get('outsourcing_potential_score', 0),
                        company['research_status'],
                        company.get('profiles_found', 0),
                        company.get('high_priority_profiles', 0),
                        company.get('medium_priority_profiles', 0),
                        company.get('low_priority_profiles', 0),
                        round(company.get('avg_relevance_score', 0) or 0, 1)
                    ])
            
            # Profiles data CSV
            profiles_filename = filename.replace('.csv', '_profiles.csv')
            with open(profiles_filename, 'w', newline='', encoding='utf-8') as csvfile:
                profile_writer = csv.writer(csvfile)
                profile_writer.writerow([
                    'Company Name', 'City', 'Profile Name', 'Profile Title', 'Profile URL',
                    'Location', 'Relevance Score', 'Contact Priority', 'Search Query'
                ])
                
                for company in companies:
                    profiles = self.get_profiles_for_company(company['id'])
                    for profile in profiles:
                        profile_writer.writerow([
                            company['company_name'],
                            company['city'],
                            profile['profile_name'],
                            profile.get('profile_title', ''),
                            profile['profile_url'],
                            profile.get('profile_location', ''),
                            profile['relevance_score'],
                            profile['contact_priority'],
                            profile.get('search_query', '')
                        ])
            
            logging.info(f"Data exported to {filename} and {profiles_filename}")
            return filename
            
        except Exception as e:
            logging.error(f"Error exporting to CSV: {e}")
            return ""
    
    def generate_summary_stats(self) -> Dict:
        """Generate summary statistics"""
        companies = self.get_companies_with_profiles()
        
        if not companies:
            return {}
        
        stats = {
            'total_companies': len(companies),
            'completed_research': len([c for c in companies if c['research_status'] == 'completed']),
            'pending_research': len([c for c in companies if c['research_status'] == 'pending']),
            'failed_research': len([c for c in companies if c['research_status'] == 'failed']),
            'total_profiles': sum(company.get('profiles_found', 0) for company in companies),
            'high_priority_profiles': sum(company.get('high_priority_profiles', 0) for company in companies),
            'medium_priority_profiles': sum(company.get('medium_priority_profiles', 0) for company in companies),
            'low_priority_profiles': sum(company.get('low_priority_profiles', 0) for company in companies),
            'avg_profiles_per_company': sum(company.get('profiles_found', 0) for company in companies) / len(companies) if companies else 0,
            'top_companies': []
        }
        
        # Get top companies by outsourcing potential
        top_companies = sorted(companies, key=lambda x: x.get('outsourcing_potential_score', 0), reverse=True)[:5]
        for company in top_companies:
            stats['top_companies'].append({
                'name': company['company_name'],
                'city': company['city'],
                'score': company.get('outsourcing_potential_score', 0),
                'profiles': company.get('profiles_found', 0)
            })
        
        return stats

def main():
    """Main function"""
    try:
        generator = ReportGenerator()
        
        print("📊 Generating LinkedIn Prospects Report...")
        
        # Generate summary statistics
        stats = generator.generate_summary_stats()
        
        if not stats:
            print("❌ No data found in the test database.")
            return
        
        # Print summary
        print(f"\n📈 SUMMARY STATISTICS:")
        print(f"   Companies: {stats['total_companies']}")
        print(f"   Research Completed: {stats['completed_research']}")
        print(f"   Total Profiles: {stats['total_profiles']}")
        print(f"   High Priority Contacts: {stats['high_priority_profiles']}")
        print(f"   Avg Profiles per Company: {stats['avg_profiles_per_company']:.1f}")
        
        # Generate text report
        report = generator.generate_text_report()
        
        # Save report to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f"linkedin_test/linkedin_report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 Text report saved: {report_filename}")
        
        # Export to CSV
        csv_filename = generator.export_to_csv()
        if csv_filename:
            print(f"📊 CSV data exported: {csv_filename}")
        
        # Show top companies
        print(f"\n🏆 TOP COMPANIES BY OUTSOURCING POTENTIAL:")
        for i, company in enumerate(stats['top_companies'], 1):
            print(f"   {i}. {company['name']} ({company['city']}) - Score: {company['score']}, Profiles: {company['profiles']}")
        
        print(f"\n✅ Report generation completed!")
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        logging.error(f"Error in main: {e}")

if __name__ == "__main__":
    main()
