#!/usr/bin/env python3
"""
Test Google Custom Search API
"""

import sys
import os
import requests
import yaml
import json

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_google_search():
    """Test Google Custom Search API with various queries"""
    
    # Load config
    with open('linkedin_test/test_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    google_config = config.get('google_search', {})
    api_key = google_config.get('api_key')
    search_engine_id = google_config.get('search_engine_id')
    
    if not api_key:
        print("❌ No Google API key found")
        return
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    print(f"🔍 Using search engine ID: {search_engine_id}")
    
    # Test queries
    test_queries = [
        "linkedin.com/in/ CTO",
        "site:linkedin.com/in/ CTO",
        "ZipHQ CTO linkedin",
        "linkedin profiles CTO",
        "python programming"  # Simple test query
    ]
    
    search_url = "https://www.googleapis.com/customsearch/v1"
    
    for query in test_queries:
        print(f"\n🔍 Testing query: {query}")
        
        params = {
            'key': api_key,
            'cx': search_engine_id,
            'q': query,
            'num': 5,
            'safe': 'off'
        }
        
        try:
            response = requests.get(search_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])
                
                print(f"   ✅ Found {len(items)} results")
                
                for i, item in enumerate(items[:3], 1):
                    title = item.get('title', '')[:60]
                    link = item.get('link', '')
                    print(f"   {i}. {title}...")
                    print(f"      {link}")
                    
                    if 'linkedin.com/in/' in link:
                        print(f"      🎯 LinkedIn profile found!")
                        
            else:
                print(f"   ❌ Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    test_google_search()
