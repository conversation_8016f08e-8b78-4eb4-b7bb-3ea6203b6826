# LinkedIn Test Configuration
# Configuration for extracting companies and finding LinkedIn profiles

# Company extraction settings
company_extraction:
  max_companies: 10
  min_unique_positions: 3
  days_back: 30  # Look back 30 days for companies that were sent in emails
  exclude_companies:
    - "recruitment"
    - "staffing"
    - "consulting"
    - "temp"
    - "agency"
  
  # Industries to prioritize for outsourcing potential
  priority_industries:
    - "software"
    - "technology"
    - "fintech"
    - "e-commerce"
    - "startup"
    - "saas"
    - "digital"

# LinkedIn search settings
linkedin_search:
  # Target roles for outsourcing contacts
  target_roles:
    - "CTO"
    - "VP Engineering"
    - "Head of Engineering"
    - "Engineering Manager"
    - "Technical Director"
    - "VP Technology"
    - "Chief Technology Officer"
    - "Head of Development"
    - "Engineering Lead"
    - "Technical Lead"
  
  # Search query templates
  search_templates:
    - "{company_name} {city} {role} site:linkedin.com"
    - "{company_name} {role} {city} linkedin"
    - "site:linkedin.com/in/ {company_name} {role}"
    - "{role} {company_name} {city} linkedin profile"
  
  # Search engine settings
  search_engine: "google"
  max_results_per_query: 10
  max_profiles_per_company: 5
  delay_between_searches: 2  # seconds
  
  # Profile validation
  min_relevance_score: 60
  verify_profiles: true

# Google Search API settings
google_search:
  api_key: "AIzaSyBq7h3xaBuRlSwztWZdZ9bSZN_PDDNGVEo"
  search_engine_id: "017576662512468239146:omuauf_lfve"  # Custom search engine for web search
  use_custom_search: true
  
# Web scraping settings for profile extraction
web_scraping:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  timeout: 10
  max_retries: 3
  delay_between_requests: 1

# Scoring and prioritization
scoring:
  # Factors for relevance scoring (0-100)
  role_match_weight: 40
  company_match_weight: 30
  location_match_weight: 20
  profile_completeness_weight: 10
  
  # Priority thresholds
  high_priority_threshold: 80
  medium_priority_threshold: 60

# Output and reporting
output:
  save_to_database: true
  generate_report: true
  export_csv: true
  log_level: "INFO"
