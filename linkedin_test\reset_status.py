#!/usr/bin/env python3
"""
Reset company research status to pending
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from heroku_config import load_heroku_config

def main():
    config = load_heroku_config()
    db = DatabaseManager(config)
    cursor = db.connection.cursor()
    
    # Reset all companies to pending status
    cursor.execute("UPDATE test_companies SET research_status = 'pending'")
    db.connection.commit()
    
    # Check how many companies were updated
    cursor.execute("SELECT COUNT(*) FROM test_companies WHERE research_status = 'pending'")
    count = cursor.fetchone()[0]
    
    cursor.close()
    db.close()
    
    print(f"✅ Reset {count} companies to pending status")

if __name__ == "__main__":
    main()
