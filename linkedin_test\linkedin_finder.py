#!/usr/bin/env python3
"""
LinkedIn Profile Finder
Uses Google search to find LinkedIn profiles of relevant people for outsourcing opportunities
"""

import sys
import os
import logging
import yaml
import json
import time
import re
from typing import List, Dict, Optional
from urllib.parse import quote_plus, urlparse
from bs4 import BeautifulSoup
from scrapfly import ScrapeConfig, ScrapflyClient

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from heroku_config import load_heroku_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("linkedin_test/linkedin_search.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class LinkedInFinder:
    def __init__(self, config_path: str = "linkedin_test/test_config.yaml"):
        """Initialize the LinkedIn finder"""
        self.main_config = load_heroku_config()
        self.test_config = self.load_test_config(config_path)
        self.db_manager = DatabaseManager(self.main_config)

        # Setup Scrapfly client for web scraping
        self.scrapfly_client = ScrapflyClient(key=self.main_config['scrapfly']['api_key'])

        # Web scraping configuration
        web_config = self.test_config.get('web_scraping', {})
        self.timeout = web_config.get('timeout', 10)
        self.max_retries = web_config.get('max_retries', 3)
        self.delay = web_config.get('delay_between_requests', 1)
        
    def load_test_config(self, config_path: str) -> Dict:
        """Load test configuration"""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logging.error(f"Test config file not found: {config_path}")
            raise
        except Exception as e:
            logging.error(f"Error loading test config: {e}")
            raise
    
    def get_companies_for_research(self) -> List[Dict]:
        """Get companies from test_companies table that need LinkedIn research"""
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            query = """
            SELECT id, company_name, city, unique_positions, total_jobs,
                   source_sites, sample_job_titles, industry_sector,
                   company_size_estimate, outsourcing_potential_score
            FROM test_companies 
            WHERE research_status = 'pending'
            ORDER BY outsourcing_potential_score DESC, unique_positions DESC
            """
            
            cursor.execute(query)
            companies = cursor.fetchall()
            cursor.close()
            
            logging.info(f"Found {len(companies)} companies pending LinkedIn research")
            return companies
            
        except Exception as e:
            logging.error(f"Error getting companies for research: {e}")
            return []
    
    def generate_search_queries(self, company: Dict) -> List[str]:
        """Generate Google search queries for finding LinkedIn profiles"""
        search_config = self.test_config.get('linkedin_search', {})
        target_roles = search_config.get('target_roles', ['CTO', 'VP Engineering'])
        search_templates = search_config.get('search_templates', [])
        
        company_name = company['company_name']
        city = company['city']
        
        queries = []
        
        # Generate queries for each role and template combination
        for role in target_roles[:3]:  # Limit to top 3 roles to avoid too many queries
            for template in search_templates[:2]:  # Limit to top 2 templates
                query = template.format(
                    company_name=company_name,
                    city=city,
                    role=role
                )
                queries.append(query)
        
        # Add some general queries
        queries.extend([
            f'"{company_name}" {city} CTO OR "VP Engineering" OR "Head of Engineering" site:linkedin.com',
            f'site:linkedin.com/in/ "{company_name}" engineering manager {city}'
        ])
        
        logging.info(f"Generated {len(queries)} search queries for {company_name}")
        return queries
    
    def search_google(self, query: str, max_results: int = 10) -> List[Dict]:
        """
        Perform Google search using Scrapfly and extract LinkedIn profile URLs
        """
        try:
            # Encode query for URL
            encoded_query = quote_plus(query)
            search_url = f"https://www.google.com/search?q={encoded_query}&num={max_results}"

            # Add delay to avoid rate limiting
            time.sleep(self.delay)

            # Use Scrapfly to scrape Google search results
            scrape_config = ScrapeConfig(
                url=search_url,
                render_js=False,  # Google search doesn't need JS rendering
                country='US',
                retry=True,
                timeout=self.timeout * 1000,  # Scrapfly expects milliseconds
                headers={
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                }
            )

            response = self.scrapfly_client.scrape(scrape_config)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract search result links
            results = []
            search_results = soup.find_all('div', class_='g')

            for result in search_results:
                link_element = result.find('a')
                if link_element and link_element.get('href'):
                    url = link_element.get('href')

                    # Filter for LinkedIn profile URLs
                    if 'linkedin.com/in/' in url and '/pub/' not in url:
                        title_element = result.find('h3')
                        title = title_element.get_text() if title_element else ''

                        snippet_element = result.find('span', class_='aCOpRe')
                        if not snippet_element:
                            snippet_element = result.find('div', class_='VwiC3b')
                        snippet = snippet_element.get_text() if snippet_element else ''

                        results.append({
                            'url': url,
                            'title': title,
                            'snippet': snippet
                        })

            logging.info(f"Found {len(results)} LinkedIn profiles for query: {query[:50]}...")
            return results

        except Exception as e:
            logging.error(f"Error searching Google with Scrapfly for query '{query}': {e}")
            return []
    
    def extract_profile_info(self, profile_url: str) -> Optional[Dict]:
        """
        Extract basic information from LinkedIn profile URL
        Note: LinkedIn has anti-scraping measures, this is a basic implementation
        """
        try:
            # Extract profile name from URL
            profile_match = re.search(r'/in/([^/?]+)', profile_url)
            if not profile_match:
                return None
            
            profile_slug = profile_match.group(1)
            
            # Basic profile info extraction (limited due to LinkedIn's restrictions)
            profile_info = {
                'profile_url': profile_url,
                'profile_slug': profile_slug,
                'profile_name': profile_slug.replace('-', ' ').title(),
                'profile_title': '',
                'profile_location': '',
                'profile_summary': '',
                'extraction_method': 'url_parsing'
            }
            
            return profile_info
            
        except Exception as e:
            logging.error(f"Error extracting profile info from {profile_url}: {e}")
            return None
    
    def calculate_relevance_score(self, profile: Dict, company: Dict, search_query: str) -> int:
        """Calculate relevance score for a LinkedIn profile"""
        scoring_config = self.test_config.get('scoring', {})
        
        score = 0
        
        # Role match scoring
        target_roles = self.test_config.get('linkedin_search', {}).get('target_roles', [])
        profile_title = profile.get('title', '').lower()
        
        for role in target_roles:
            if role.lower() in profile_title:
                score += scoring_config.get('role_match_weight', 40)
                break
        
        # Company match scoring
        company_name = company['company_name'].lower()
        if company_name in profile.get('snippet', '').lower():
            score += scoring_config.get('company_match_weight', 30)
        
        # Location match scoring
        city = company['city'].lower()
        profile_location = profile.get('profile_location', '').lower()
        if city in profile_location or city in profile.get('snippet', '').lower():
            score += scoring_config.get('location_match_weight', 20)
        
        # Profile completeness scoring
        if profile.get('profile_title') and profile.get('profile_summary'):
            score += scoring_config.get('profile_completeness_weight', 10)
        
        return min(100, score)
    
    def save_search_query(self, company_id: int, query: str, results_count: int, 
                         profiles_extracted: int, success: bool, execution_time: int = 0, 
                         error_message: str = None):
        """Save search query results for tracking and optimization"""
        try:
            cursor = self.db_manager.connection.cursor()
            
            insert_query = """
            INSERT INTO search_queries (
                company_id, query_text, search_engine, results_found,
                profiles_extracted, query_success, execution_time_ms, error_message
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                company_id, query, 'google', results_count,
                profiles_extracted, success, execution_time, error_message
            ))
            
            self.db_manager.connection.commit()
            cursor.close()
            
        except Exception as e:
            logging.error(f"Error saving search query: {e}")
    
    def save_linkedin_profile(self, company_id: int, company_name: str, city: str,
                            profile: Dict, relevance_score: int, search_query: str) -> bool:
        """Save LinkedIn profile to database"""
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Determine contact priority based on relevance score
            scoring_config = self.test_config.get('scoring', {})
            high_threshold = scoring_config.get('high_priority_threshold', 80)
            medium_threshold = scoring_config.get('medium_priority_threshold', 60)
            
            if relevance_score >= high_threshold:
                priority = 'high'
            elif relevance_score >= medium_threshold:
                priority = 'medium'
            else:
                priority = 'low'
            
            insert_query = """
            INSERT INTO linkedin_profiles (
                company_id, company_name, city, profile_name, profile_title,
                profile_url, profile_location, profile_summary, relevance_score,
                contact_priority, search_query, search_source, extraction_method
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                company_id, company_name, city,
                profile.get('profile_name', ''),
                profile.get('profile_title', ''),
                profile.get('profile_url', ''),
                profile.get('profile_location', ''),
                profile.get('profile_summary', ''),
                relevance_score, priority, search_query,
                'google', profile.get('extraction_method', 'web_search')
            ))
            
            self.db_manager.connection.commit()
            cursor.close()
            
            return True
            
        except Exception as e:
            logging.error(f"Error saving LinkedIn profile: {e}")
            return False
    
    def update_company_research_status(self, company_id: int, status: str):
        """Update research status for a company"""
        try:
            cursor = self.db_manager.connection.cursor()
            
            update_query = """
            UPDATE test_companies 
            SET research_status = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
            """
            
            cursor.execute(update_query, (status, company_id))
            self.db_manager.connection.commit()
            cursor.close()
            
        except Exception as e:
            logging.error(f"Error updating company research status: {e}")
    
    def research_company_profiles(self, company: Dict) -> Dict:
        """Research LinkedIn profiles for a specific company"""
        company_id = company['id']
        company_name = company['company_name']
        city = company['city']
        
        logging.info(f"Starting LinkedIn research for {company_name} ({city})")
        
        # Update status to in_progress
        self.update_company_research_status(company_id, 'in_progress')
        
        try:
            # Generate search queries
            queries = self.generate_search_queries(company)
            
            total_profiles_found = 0
            total_profiles_saved = 0
            
            search_config = self.test_config.get('linkedin_search', {})
            max_results_per_query = search_config.get('max_results_per_query', 10)
            max_profiles_per_company = search_config.get('max_profiles_per_company', 5)
            delay_between_searches = search_config.get('delay_between_searches', 2)
            min_relevance_score = search_config.get('min_relevance_score', 60)
            
            for query in queries:
                if total_profiles_saved >= max_profiles_per_company:
                    break
                
                start_time = time.time()
                
                # Perform search
                search_results = self.search_google(query, max_results_per_query)
                total_profiles_found += len(search_results)
                
                profiles_extracted_this_query = 0
                
                for result in search_results:
                    if total_profiles_saved >= max_profiles_per_company:
                        break
                    
                    # Extract profile information
                    profile_info = self.extract_profile_info(result['url'])
                    if not profile_info:
                        continue
                    
                    # Add search result data to profile info
                    profile_info.update({
                        'title': result['title'],
                        'snippet': result['snippet']
                    })
                    
                    # Calculate relevance score
                    relevance_score = self.calculate_relevance_score(profile_info, company, query)
                    
                    # Only save profiles above minimum relevance threshold
                    if relevance_score >= min_relevance_score:
                        if self.save_linkedin_profile(
                            company_id, company_name, city, profile_info, relevance_score, query
                        ):
                            total_profiles_saved += 1
                            profiles_extracted_this_query += 1
                            logging.info(f"Saved profile: {profile_info.get('profile_name', 'Unknown')} (Score: {relevance_score})")
                
                # Save search query results
                execution_time = int((time.time() - start_time) * 1000)
                self.save_search_query(
                    company_id, query, len(search_results), 
                    profiles_extracted_this_query, True, execution_time
                )
                
                # Delay between searches
                time.sleep(delay_between_searches)
            
            # Update status to completed
            self.update_company_research_status(company_id, 'completed')
            
            result = {
                'success': True,
                'company_name': company_name,
                'profiles_found': total_profiles_found,
                'profiles_saved': total_profiles_saved,
                'queries_executed': len(queries)
            }
            
            logging.info(f"Completed LinkedIn research for {company_name}: {total_profiles_saved} profiles saved")
            return result
            
        except Exception as e:
            logging.error(f"Error researching company {company_name}: {e}")
            self.update_company_research_status(company_id, 'failed')
            return {
                'success': False,
                'company_name': company_name,
                'error': str(e)
            }
    
    def run_linkedin_research(self) -> Dict:
        """Run LinkedIn research for all pending companies"""
        try:
            logging.info("Starting LinkedIn profile research...")
            
            # Get companies that need research
            companies = self.get_companies_for_research()
            
            if not companies:
                logging.info("No companies pending LinkedIn research")
                return {'success': True, 'message': 'No companies pending research'}
            
            results = []
            total_profiles_saved = 0
            
            for company in companies:
                result = self.research_company_profiles(company)
                results.append(result)
                
                if result['success']:
                    total_profiles_saved += result['profiles_saved']
                
                # Add delay between companies to be respectful to search engines
                time.sleep(5)
            
            summary = {
                'success': True,
                'companies_researched': len(companies),
                'total_profiles_saved': total_profiles_saved,
                'results': results
            }
            
            logging.info(f"LinkedIn research completed: {total_profiles_saved} profiles saved for {len(companies)} companies")
            return summary
            
        except Exception as e:
            logging.error(f"Error in LinkedIn research: {e}")
            return {'success': False, 'error': str(e)}
        
        finally:
            self.db_manager.close()

def main():
    """Main function"""
    try:
        finder = LinkedInFinder()
        result = finder.run_linkedin_research()
        
        if result['success']:
            if 'message' in result:
                print(f"ℹ️  {result['message']}")
            else:
                print(f"✅ LinkedIn research completed successfully!")
                print(f"🏢 Companies researched: {result['companies_researched']}")
                print(f"👥 Total profiles saved: {result['total_profiles_saved']}")
                
                print("\n📊 Results by company:")
                for company_result in result['results']:
                    if company_result['success']:
                        print(f"  • {company_result['company_name']}: {company_result['profiles_saved']} profiles")
                    else:
                        print(f"  • {company_result['company_name']}: Failed - {company_result['error']}")
        else:
            print(f"❌ LinkedIn research failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Critical error: {e}")
        logging.error(f"Critical error in main: {e}")

if __name__ == "__main__":
    main()
