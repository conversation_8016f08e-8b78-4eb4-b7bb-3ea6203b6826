-- LinkedIn Test Database Schema
-- Tables for storing companies and LinkedIn profiles for outsourcing prospects

-- Table to store selected companies for LinkedIn research
CREATE TABLE IF NOT EXISTS test_companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    unique_positions INT NOT NULL,
    total_jobs INT NOT NULL,
    source_sites JSON,
    sample_job_titles JSON,
    industry_sector VARCHAR(100),
    company_size_estimate VARCHAR(50),
    outsourcing_potential_score INT DEFAULT 0,
    research_status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_city (company_name, city),
    INDEX idx_research_status (research_status),
    INDEX idx_created_at (created_at)
);

-- Table to store LinkedIn profiles found for each company
CREATE TABLE IF NOT EXISTS linkedin_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    profile_name VARCHAR(255) NOT NULL,
    profile_title VARCHAR(255),
    profile_url VARCHAR(1024) NOT NULL,
    profile_location VARCHAR(255),
    profile_summary TEXT,
    relevance_score INT DEFAULT 0,
    contact_priority ENUM('high', 'medium', 'low') DEFAULT 'medium',
    search_query VARCHAR(500),
    search_source VARCHAR(100) DEFAULT 'google',
    extraction_method VARCHAR(100) DEFAULT 'web_search',
    profile_verified BOOLEAN DEFAULT FALSE,
    contact_attempted BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES test_companies(id) ON DELETE CASCADE,
    INDEX idx_company_id (company_id),
    INDEX idx_profile_url (profile_url(255)),
    INDEX idx_relevance_score (relevance_score),
    INDEX idx_contact_priority (contact_priority),
    INDEX idx_created_at (created_at)
);

-- Table to track search queries and results for optimization
CREATE TABLE IF NOT EXISTS search_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    query_text VARCHAR(500) NOT NULL,
    search_engine VARCHAR(50) DEFAULT 'google',
    results_found INT DEFAULT 0,
    profiles_extracted INT DEFAULT 0,
    query_success BOOLEAN DEFAULT FALSE,
    execution_time_ms INT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES test_companies(id) ON DELETE CASCADE,
    INDEX idx_company_id (company_id),
    INDEX idx_query_success (query_success),
    INDEX idx_created_at (created_at)
);
