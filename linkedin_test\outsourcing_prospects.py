#!/usr/bin/env python3
"""
Outsourcing Prospects LinkedIn Finder
Extracts companies from latest HubSimplified report and finds LinkedIn profiles for outsourcing prospects
"""

import sys
import os
import logging
import json
import time
import re
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
from scrapfly import ScrapeConfig, ScrapflyClient

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from heroku_config import load_heroku_config
from email_sender import EmailSender

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("linkedin_test/outsourcing_prospects.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OutsourcingProspectsFinder:
    def __init__(self):
        """Initialize the outsourcing prospects finder"""
        self.config = load_heroku_config()
        self.db_manager = DatabaseManager(self.config)
        self.scrapfly_client = ScrapflyClient(key=self.config['scrapfly']['api_key'])
        self.email_sender = EmailSender(self.config)
        
        # Target job titles for outsourcing prospects (companies up to 10,000 employees)
        self.target_job_titles = [
            "CTO",
            "VP Engineering", 
            "VP of Engineering",
            "Head of Engineering",
            "Engineering Director",
            "Technical Director",
            "VP Technology",
            "Chief Technology Officer",
            "Head of Development",
            "Engineering Manager"
        ]
        
        # Search delay to be respectful
        self.search_delay = 3  # seconds between searches
        
    def get_latest_hubsimplified_report(self) -> Optional[Dict]:
        """Get the latest HubSimplified report"""
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            query = """
            SELECT id, analysis_text, created_at, date_range_start, date_range_end
            FROM hub_simplified_reports
            ORDER BY created_at DESC
            LIMIT 1
            """
            
            cursor.execute(query)
            report = cursor.fetchone()
            cursor.close()
            
            if report:
                logging.info(f"Found latest HubSimplified report from {report['created_at']}")
                return report
            else:
                logging.error("No HubSimplified reports found")
                return None
                
        except Exception as e:
            logging.error(f"Error getting latest HubSimplified report: {e}")
            return None
    
    def extract_companies_from_analysis(self, analysis_text: str) -> List[Dict]:
        """Extract company and city pairs from analysis text"""
        try:
            companies = []

            # Split analysis by city sections (=== CITY ===)
            city_sections = re.split(r'=== ([A-Z\s]+) ===', analysis_text)

            current_city = None
            for i, section in enumerate(city_sections):
                # Check if this section is a city header
                if i % 2 == 1:  # Odd indices are city names from the split
                    current_city = section.strip()
                    continue

                if current_city and section.strip():
                    # Extract company names from this city section
                    # Look for pattern: • Company Name
                    company_matches = re.findall(r'• ([^•\n]+?)(?:\n|$)', section, re.MULTILINE)

                    for company_match in company_matches:
                        company_name = company_match.strip()

                        # Clean up company name (remove trailing dashes and extra text)
                        company_name = re.sub(r'\s*-.*$', '', company_name)  # Remove everything after dash
                        company_name = company_name.strip()

                        # Filter out job titles and other non-company text
                        if (len(company_name) > 2 and
                            not company_name.lower().startswith(('template', 'admin', 'php', 'javascript', 'software', 'senior', 'junior', 'tech', 'fullstack', 'backend', 'frontend')) and
                            not re.match(r'^[a-z]', company_name) and  # Should start with capital
                            'GmbH' in company_name or 'AG' in company_name or 'Group' in company_name or 'Inc' in company_name or 'Ltd' in company_name or len(company_name.split()) <= 4):

                            companies.append({
                                'company': company_name,
                                'city': current_city
                            })

            # Remove duplicates
            unique_companies = []
            seen = set()
            for company in companies:
                key = (company['company'].lower(), company['city'].lower())
                if key not in seen:
                    seen.add(key)
                    unique_companies.append(company)

            logging.info(f"Extracted {len(unique_companies)} unique companies from analysis")

            # Debug: print first few companies
            for i, company in enumerate(unique_companies[:5]):
                logging.info(f"Company {i+1}: {company['company']} in {company['city']}")

            return unique_companies

        except Exception as e:
            logging.error(f"Error extracting companies from analysis: {e}")
            return []
    
    def search_linkedin_profiles(self, company: str, city: str) -> List[Dict]:
        """Search for LinkedIn profiles for a specific company and city"""
        try:
            profiles = []
            
            # Create search queries for different job titles
            for job_title in self.target_job_titles[:3]:  # Limit to top 3 titles to avoid too many requests
                query = f'"{company}" {city} "{job_title}" site:linkedin.com'
                encoded_query = quote_plus(query)
                search_url = f"https://www.google.com/search?q={encoded_query}&num=5"
                
                logging.info(f"Searching: {query}")
                
                # Add delay between searches
                time.sleep(self.search_delay)
                
                # Use Scrapfly to search
                scrape_config = ScrapeConfig(
                    url=search_url,
                    render_js=True,
                    country='US',
                    retry=True,
                    wait_for_selector='div.tF2Cxc',
                    headers={
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                    }
                )
                
                response = self.scrapfly_client.scrape(scrape_config)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract LinkedIn profiles
                search_results = soup.find_all('div', class_='tF2Cxc')
                
                for result in search_results:
                    link_elements = result.find_all('a', href=True)
                    
                    for link_element in link_elements:
                        url = link_element.get('href')
                        
                        if 'linkedin.com/in/' in url and '/pub/' not in url:
                            # Extract profile info
                            title_element = (result.find('h3') or 
                                           link_element.find('h3') or 
                                           result.find('h1'))
                            title = title_element.get_text() if title_element else ''
                            
                            snippet_element = (result.find('span', class_='aCOpRe') or
                                             result.find('div', class_='VwiC3b') or
                                             result.find('div', class_='IsZvec'))
                            snippet = snippet_element.get_text() if snippet_element else ''
                            
                            # Calculate relevance score
                            relevance_score = 0
                            title_lower = title.lower()
                            snippet_lower = snippet.lower()
                            company_lower = company.lower()
                            city_lower = city.lower()
                            
                            # Job title match (40 points)
                            if job_title.lower() in title_lower:
                                relevance_score += 40
                            elif any(word in title_lower for word in ['cto', 'vp', 'head', 'director', 'chief']):
                                relevance_score += 30
                            
                            # Company match (30 points)
                            if company_lower in snippet_lower or company_lower in title_lower:
                                relevance_score += 30
                            
                            # Location match (20 points)
                            if city_lower in snippet_lower or city_lower in title_lower:
                                relevance_score += 20
                            
                            # Profile completeness (10 points)
                            if title and snippet:
                                relevance_score += 10
                            
                            # Only include profiles with decent relevance
                            if relevance_score >= 50:
                                profiles.append({
                                    'company': company,
                                    'city': city,
                                    'name': title,
                                    'title': job_title,
                                    'url': url,
                                    'snippet': snippet,
                                    'relevance_score': relevance_score,
                                    'search_query': query
                                })
                                logging.info(f"Found profile: {title} (Score: {relevance_score})")
                            
                            break  # Only take first LinkedIn URL per result
                
                # Limit profiles per company to avoid overwhelming results
                if len(profiles) >= 3:
                    break
            
            return profiles
            
        except Exception as e:
            logging.error(f"Error searching LinkedIn profiles for {company} in {city}: {e}")
            return []
    
    def generate_email_report(self, all_profiles: List[Dict]) -> str:
        """Generate email report with LinkedIn profiles"""
        try:
            # Group profiles by company and city
            companies = {}
            for profile in all_profiles:
                key = f"{profile['company']} ({profile['city']})"
                if key not in companies:
                    companies[key] = []
                companies[key].append(profile)
            
            # Generate HTML email
            html_content = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #1a73e8; }}
                    h2 {{ color: #34a853; margin-top: 30px; }}
                    .profile {{ margin: 15px 0; padding: 15px; border-left: 3px solid #1a73e8; background-color: #f8f9fa; }}
                    .profile-name {{ font-weight: bold; font-size: 16px; }}
                    .profile-title {{ color: #5f6368; font-style: italic; }}
                    .profile-url {{ margin: 5px 0; }}
                    .profile-url a {{ color: #1a73e8; text-decoration: none; }}
                    .profile-snippet {{ margin: 10px 0; color: #3c4043; }}
                    .score {{ float: right; background-color: #34a853; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; }}
                    .summary {{ background-color: #e8f0fe; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
                </style>
            </head>
            <body>
                <h1>🎯 Outsourcing Prospects - LinkedIn Profiles</h1>
                
                <div class="summary">
                    <strong>📊 Summary:</strong><br>
                    • Total Companies: {len(companies)}<br>
                    • Total Profiles Found: {len(all_profiles)}<br>
                    • Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
                    • Source: Latest HubSimplified Report
                </div>
            """
            
            for company_key, profiles in companies.items():
                html_content += f"<h2>🏢 {company_key}</h2>"
                
                for profile in profiles:
                    html_content += f"""
                    <div class="profile">
                        <div class="score">Score: {profile['relevance_score']}</div>
                        <div class="profile-name">{profile['name']}</div>
                        <div class="profile-title">{profile['title']}</div>
                        <div class="profile-url">
                            <a href="{profile['url']}" target="_blank">{profile['url']}</a>
                        </div>
                        <div class="profile-snippet">{profile['snippet'][:200]}...</div>
                    </div>
                    """
            
            html_content += """
                <hr style="margin: 30px 0;">
                <p style="color: #5f6368; font-size: 12px;">
                    This report was automatically generated by the HubScanner LinkedIn Prospects system.<br>
                    Profiles are scored based on job title relevance, company match, and location match.
                </p>
            </body>
            </html>
            """
            
            return html_content
            
        except Exception as e:
            logging.error(f"Error generating email report: {e}")
            return ""
    
    def run_outsourcing_prospects_search(self):
        """Run the complete outsourcing prospects search"""
        try:
            logging.info("Starting outsourcing prospects LinkedIn search...")
            
            # Get latest HubSimplified report
            report = self.get_latest_hubsimplified_report()
            if not report:
                logging.error("No HubSimplified report found")
                return
            
            # Extract companies from analysis
            companies = self.extract_companies_from_analysis(report['analysis_text'])
            if not companies:
                logging.error("No companies extracted from analysis")
                return
            
            logging.info(f"Found {len(companies)} companies to research")
            
            # Search LinkedIn profiles for each company
            all_profiles = []

            for i, company_data in enumerate(companies, 1):
                try:
                    company = company_data['company']
                    city = company_data['city']

                    logging.info(f"Researching {i}/{len(companies)}: {company} in {city}")

                    profiles = self.search_linkedin_profiles(company, city)
                    all_profiles.extend(profiles)

                    # Add delay between companies
                    time.sleep(2)

                    # Send interim report every 10 companies or if we have 20+ profiles
                    if i % 10 == 0 or len(all_profiles) >= 20:
                        logging.info(f"Interim checkpoint: {len(all_profiles)} profiles found so far")

                except KeyboardInterrupt:
                    logging.info(f"Process interrupted after {i-1} companies. Sending results so far...")
                    break
                except Exception as e:
                    logging.error(f"Error processing {company} in {city}: {e}")
                    continue
            
            logging.info(f"Found {len(all_profiles)} total LinkedIn profiles")
            
            if all_profiles:
                # Generate and send email report
                email_content = self.generate_email_report(all_profiles)
                
                subject = f"🎯 Outsourcing Prospects - {len(all_profiles)} LinkedIn Profiles Found"
                
                success = self.email_sender.send_email(
                    to_email="<EMAIL>",
                    subject=subject,
                    html_content=email_content
                )
                
                if success:
                    logging.info("✅ Email report sent <NAME_EMAIL>")
                else:
                    logging.error("❌ Failed to send email report")
            else:
                logging.warning("No LinkedIn profiles found to report")
                
        except Exception as e:
            logging.error(f"Error in outsourcing prospects search: {e}")
        
        finally:
            self.db_manager.close()

def main():
    """Main function"""
    try:
        finder = OutsourcingProspectsFinder()
        finder.run_outsourcing_prospects_search()
        
    except Exception as e:
        logging.error(f"Critical error: {e}")

if __name__ == "__main__":
    main()
