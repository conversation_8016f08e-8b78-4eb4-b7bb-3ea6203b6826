#!/usr/bin/env python3
"""
Test Scrapfly with Google Search for LinkedIn profiles
"""

import sys
import os
import logging
from bs4 import BeautifulSoup
from scrapfly import ScrapeConfig, ScrapflyClient

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from heroku_config import load_heroku_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_scrapfly_search():
    """Test Scrapfly with the specific Google search URL"""
    
    try:
        # Load main config to get Scrapfly API key
        config = load_heroku_config()
        scrapfly_api_key = config['scrapfly']['api_key']
        
        print(f"🔑 Using Scrapfly API key: {scrapfly_api_key[:10]}...")
        
        # Initialize Scrapfly client
        client = ScrapflyClient(key=scrapfly_api_key)
        
        # Test URL from your request
        test_url = 'https://www.google.com/search?q=%22Amazon%22+New+York+CTO+OR+%22VP+Engineering%22+OR+%22Head+of+Engineering%22+site%3Alinkedin.com&num=10'
        
        print(f"🔍 Testing URL: {test_url}")
        
        # Configure Scrapfly request
        scrape_config = ScrapeConfig(
            url=test_url,
            render_js=True,  # Enable JavaScript rendering for Google search
            country='US',
            retry=True,
            wait_for_selector='div.g',  # Wait for search results to load
            headers={
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        
        print("📡 Sending request to Scrapfly...")
        
        # Make the request
        response = client.scrape(scrape_config)
        
        print(f"✅ Response received - Status: {response.status_code}")
        print(f"📄 Content length: {len(response.content)} bytes")
        
        # Parse the HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for Google search results with multiple possible selectors
        search_selectors = [
            'div.g',           # Traditional Google results
            'div.tF2Cxc',      # New Google results container
            'div.MjjYud',      # Another possible container
            'div[data-ved]',   # Results with data-ved attribute
            'div.yuRUbf',      # Result title container
        ]

        search_results = []
        for selector in search_selectors:
            results = soup.select(selector)
            if results:
                search_results = results
                print(f"🔍 Found {len(search_results)} search result containers using selector: {selector}")
                break

        if not search_results:
            print(f"🔍 Found 0 search result containers with any known selector")

        # Extract LinkedIn profiles
        linkedin_profiles = []

        for i, result in enumerate(search_results, 1):
            # Try multiple ways to find links
            link_elements = result.find_all('a', href=True)

            for link_element in link_elements:
                url = link_element.get('href')

                # Check if it's a LinkedIn profile
                if 'linkedin.com/in/' in url and '/pub/' not in url:
                    # Try multiple ways to find title
                    title_element = (result.find('h3') or
                                   link_element.find('h3') or
                                   result.find('h1') or
                                   result.find('[role="heading"]'))
                    title = title_element.get_text() if title_element else 'No title'

                    # Try multiple ways to find snippet
                    snippet_element = (result.find('span', class_='aCOpRe') or
                                     result.find('div', class_='VwiC3b') or
                                     result.find('div', class_='IsZvec') or
                                     result.find('span', class_='st'))
                    snippet = snippet_element.get_text() if snippet_element else 'No snippet'

                    linkedin_profiles.append({
                        'url': url,
                        'title': title,
                        'snippet': snippet
                    })

                    print(f"\n🎯 LinkedIn Profile {len(linkedin_profiles)}:")
                    print(f"   Title: {title}")
                    print(f"   URL: {url}")
                    print(f"   Snippet: {snippet[:100]}...")
                    break  # Only take first LinkedIn URL per result
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total search results: {len(search_results)}")
        print(f"   LinkedIn profiles found: {len(linkedin_profiles)}")
        
        if len(linkedin_profiles) == 0:
            print("\n🔍 DEBUG: Let's check what we actually got...")
            
            # Check for any links at all
            all_links = soup.find_all('a', href=True)
            linkedin_links = [link for link in all_links if 'linkedin.com' in link.get('href', '')]
            
            print(f"   Total links found: {len(all_links)}")
            print(f"   LinkedIn links found: {len(linkedin_links)}")
            
            if linkedin_links:
                print("   LinkedIn links:")
                for link in linkedin_links[:5]:
                    print(f"     {link.get('href')}")
            
            # Check if we got blocked or redirected
            title = soup.find('title')
            if title:
                print(f"   Page title: {title.get_text()}")
            
            # Look for any error messages
            error_divs = soup.find_all('div', class_=['error', 'blocked', 'captcha'])
            if error_divs:
                print("   Possible errors found:")
                for error in error_divs:
                    print(f"     {error.get_text()[:100]}")
        
        # Save raw HTML for debugging
        with open('linkedin_test/scrapfly_debug.html', 'w', encoding='utf-8') as f:
            f.write(response.content)
        print(f"\n💾 Raw HTML saved to linkedin_test/scrapfly_debug.html")
        
        return linkedin_profiles
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logging.error(f"Error in test_scrapfly_search: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_alternative_queries():
    """Test with simpler queries to see if Scrapfly works at all"""
    
    try:
        config = load_heroku_config()
        client = ScrapflyClient(key=config['scrapfly']['api_key'])
        
        # Simpler test queries
        test_queries = [
            'https://www.google.com/search?q=linkedin.com%2Fin%2F+CTO&num=5',
            'https://www.google.com/search?q=site%3Alinkedin.com+CTO&num=5',
            'https://www.google.com/search?q=python+programming&num=5'  # Very simple test
        ]
        
        for i, url in enumerate(test_queries, 1):
            print(f"\n🧪 Test {i}: {url}")
            
            scrape_config = ScrapeConfig(
                url=url,
                render_js=True,
                country='US',
                retry=True,
                wait_for_selector='div.g'
            )
            
            try:
                response = client.scrape(scrape_config)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Count results
                results = soup.find_all('div', class_='g')
                linkedin_count = len([r for r in results if 'linkedin.com' in str(r)])
                
                print(f"   ✅ Status: {response.status_code}")
                print(f"   📊 Results: {len(results)} total, {linkedin_count} LinkedIn")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Setup error: {e}")

def main():
    """Main function"""
    print("🚀 Testing Scrapfly with Google Search for LinkedIn profiles")
    print("=" * 70)
    
    # Test the specific URL
    profiles = test_scrapfly_search()
    
    if len(profiles) == 0:
        print("\n🔄 No profiles found, testing with alternative queries...")
        test_alternative_queries()
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()
