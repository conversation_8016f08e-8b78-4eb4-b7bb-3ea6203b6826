#!/usr/bin/env python3
"""
Company Extractor for LinkedIn Test
Extracts companies from HubSimplified data and prepares them for LinkedIn research
"""

import sys
import os
import logging
import yaml
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from heroku_config import load_heroku_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("linkedin_test/company_extraction.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class CompanyExtractor:
    def __init__(self, config_path: str = "linkedin_test/test_config.yaml"):
        """Initialize the company extractor"""
        self.main_config = load_heroku_config()
        self.test_config = self.load_test_config(config_path)
        self.db_manager = DatabaseManager(self.main_config)
        
    def load_test_config(self, config_path: str) -> Dict:
        """Load test configuration"""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logging.error(f"Test config file not found: {config_path}")
            raise
        except Exception as e:
            logging.error(f"Error loading test config: {e}")
            raise
    
    def setup_test_tables(self):
        """Create test tables if they don't exist"""
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Read and execute schema
            with open('linkedin_test/test_schema.sql', 'r') as schema_file:
                schema_sql = schema_file.read()
                
                # Split by semicolon and execute each statement
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                for statement in statements:
                    cursor.execute(statement)
            
            self.db_manager.connection.commit()
            cursor.close()
            logging.info("Test tables created successfully")
            
        except Exception as e:
            logging.error(f"Error setting up test tables: {e}")
            raise
    
    def get_companies_from_hubsimplified(self, days_back: int = 30, min_positions: int = 3) -> List[Dict]:
        """
        Extract companies from HubSimplified reports that were actually sent in emails
        """
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)

            # Calculate date range
            date_end = datetime.now()
            date_start = date_end - timedelta(days=days_back)

            # Query to get companies from hub_simplified_companies table (companies that were actually sent in emails)
            query = """
            SELECT
                company_name as company,
                city,
                unique_positions,
                total_jobs,
                source_sites,
                sample_jobs,
                reported_at,
                date_range_start,
                date_range_end
            FROM hub_simplified_companies
            WHERE reported_at >= %s
                AND reported_at <= %s
                AND unique_positions >= %s
                AND company_name IS NOT NULL
                AND company_name != ''
                AND city IS NOT NULL
                AND city != ''
            ORDER BY unique_positions DESC, total_jobs DESC
            """

            cursor.execute(query, (date_start, date_end, min_positions))
            companies = cursor.fetchall()
            cursor.close()

            # Convert JSON fields back to Python objects
            for company in companies:
                if company['source_sites']:
                    company['source_sites'] = json.loads(company['source_sites'])
                if company['sample_jobs']:
                    company['sample_jobs'] = json.loads(company['sample_jobs'])

            logging.info(f"Found {len(companies)} companies from HubSimplified email reports with {min_positions}+ unique positions")
            return companies

        except Exception as e:
            logging.error(f"Error extracting companies from HubSimplified reports: {e}")
            return []
    
    def filter_companies(self, companies: List[Dict]) -> List[Dict]:
        """
        Filter companies based on exclusion criteria and prioritize by industry
        """
        extraction_config = self.test_config.get('company_extraction', {})
        exclude_keywords = extraction_config.get('exclude_companies', [])
        priority_industries = extraction_config.get('priority_industries', [])
        max_companies = extraction_config.get('max_companies', 10)

        filtered_companies = []

        for company in companies:
            company_name = company['company'].lower()

            # Check exclusion criteria
            if any(keyword.lower() in company_name for keyword in exclude_keywords):
                logging.info(f"Excluded company: {company['company']} (matches exclusion criteria)")
                continue

            # Calculate industry relevance score
            industry_score = 0
            for industry in priority_industries:
                if industry.lower() in company_name:
                    industry_score += 10

            # Add sample job titles for industry detection
            sample_jobs = company.get('sample_jobs', [])
            if isinstance(sample_jobs, str):
                sample_jobs = json.loads(sample_jobs) if sample_jobs else []

            job_titles = [job.get('title', '').lower() for job in sample_jobs]

            for title in job_titles:
                for industry in priority_industries:
                    if industry.lower() in title:
                        industry_score += 5
                        break

            company['industry_score'] = industry_score
            company['sample_job_titles'] = [job.get('title', '') for job in sample_jobs[:5]]

            filtered_companies.append(company)

        # Sort by industry score and unique positions
        filtered_companies.sort(key=lambda x: (x['industry_score'], x['unique_positions']), reverse=True)

        # Limit to max companies
        result = filtered_companies[:max_companies]

        logging.info(f"Filtered to {len(result)} companies for LinkedIn research")
        return result
    
    def save_companies_to_test_table(self, companies: List[Dict]) -> int:
        """
        Save selected companies to test_companies table
        """
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Clear existing test data
            cursor.execute("DELETE FROM test_companies")
            
            insert_query = """
            INSERT INTO test_companies (
                company_name, city, unique_positions, total_jobs,
                source_sites, sample_job_titles, industry_sector,
                company_size_estimate, outsourcing_potential_score
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            companies_saved = 0
            for company in companies:
                # Estimate company size based on job postings
                total_jobs = company['total_jobs']
                if total_jobs >= 20:
                    size_estimate = "Large (20+ positions)"
                elif total_jobs >= 10:
                    size_estimate = "Medium (10-19 positions)"
                elif total_jobs >= 5:
                    size_estimate = "Small (5-9 positions)"
                else:
                    size_estimate = "Startup (3-4 positions)"
                
                # Calculate outsourcing potential score
                potential_score = min(100, company['industry_score'] + (company['unique_positions'] * 5))
                
                # Determine industry sector
                industry_sector = "Technology"  # Default, could be enhanced with AI analysis
                
                # Handle source_sites - it might be a list or string
                source_sites = company.get('source_sites', [])
                if isinstance(source_sites, str):
                    source_sites = source_sites.split(',') if source_sites else []
                elif not isinstance(source_sites, list):
                    source_sites = []

                cursor.execute(insert_query, (
                    company['company'],
                    company['city'],
                    company['unique_positions'],
                    company['total_jobs'],
                    json.dumps(source_sites),
                    json.dumps(company['sample_job_titles']),
                    industry_sector,
                    size_estimate,
                    potential_score
                ))
                companies_saved += 1
            
            self.db_manager.connection.commit()
            cursor.close()
            
            logging.info(f"Saved {companies_saved} companies to test_companies table")
            return companies_saved
            
        except Exception as e:
            logging.error(f"Error saving companies to test table: {e}")
            return 0
    
    def run_extraction(self) -> Dict:
        """
        Run the complete company extraction process
        """
        try:
            logging.info("Starting company extraction for LinkedIn test...")
            
            # Setup test tables
            self.setup_test_tables()
            
            # Get extraction parameters
            extraction_config = self.test_config.get('company_extraction', {})
            days_back = extraction_config.get('days_back', 30)
            min_positions = extraction_config.get('min_unique_positions', 3)

            # Extract companies from HubSimplified email reports
            companies = self.get_companies_from_hubsimplified(days_back, min_positions)
            
            if not companies:
                logging.warning("No companies found in HubSimplified data")
                return {'success': False, 'message': 'No companies found'}
            
            # Filter and prioritize companies
            filtered_companies = self.filter_companies(companies)
            
            if not filtered_companies:
                logging.warning("No companies passed filtering criteria")
                return {'success': False, 'message': 'No companies passed filtering'}
            
            # Save to test table
            saved_count = self.save_companies_to_test_table(filtered_companies)
            
            result = {
                'success': True,
                'companies_found': len(companies),
                'companies_filtered': len(filtered_companies),
                'companies_saved': saved_count,
                'companies': filtered_companies
            }
            
            logging.info(f"Company extraction completed: {saved_count} companies ready for LinkedIn research")
            return result
            
        except Exception as e:
            logging.error(f"Error in company extraction: {e}")
            return {'success': False, 'error': str(e)}
        
        finally:
            self.db_manager.close()

def main():
    """Main function"""
    try:
        extractor = CompanyExtractor()
        result = extractor.run_extraction()
        
        if result['success']:
            print(f"✅ Successfully extracted {result['companies_saved']} companies")
            print(f"📊 Found {result['companies_found']} total companies")
            print(f"🔍 {result['companies_filtered']} companies passed filtering")
            print("\n📋 Companies ready for LinkedIn research:")
            for i, company in enumerate(result['companies'][:5], 1):
                print(f"  {i}. {company['company']} ({company['city']}) - {company['unique_positions']} positions")
        else:
            print(f"❌ Extraction failed: {result.get('message', result.get('error', 'Unknown error'))}")
            
    except Exception as e:
        print(f"❌ Critical error: {e}")
        logging.error(f"Critical error in main: {e}")

if __name__ == "__main__":
    main()
