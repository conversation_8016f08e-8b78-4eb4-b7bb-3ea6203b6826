# LinkedIn Outsourcing Prospects Test

This test system extracts companies from HubReport Simplified data and finds LinkedIn profiles of relevant people who could benefit from outsourcing services.

## Overview

The system consists of three main components:

1. **Company Extractor** - Extracts top 10 companies from HubSimplified data
2. **LinkedIn Finder** - Uses Google search to find LinkedIn profiles of key personnel
3. **Report Generator** - Creates comprehensive reports of findings

## Files Structure

```
linkedin_test/
├── README.md                 # This file
├── test_config.yaml         # Configuration settings
├── test_schema.sql          # Database schema for test tables
├── company_extractor.py     # Extracts companies from HubSimplified
├── linkedin_finder.py       # Finds LinkedIn profiles via Google search
├── report_generator.py      # Generates reports and exports data
├── run_test.py             # Main test runner script
└── logs/                   # Generated log files
    ├── company_extraction.log
    ├── linkedin_search.log
    └── test_run.log
```

## Database Tables Created

### test_companies
Stores selected companies for LinkedIn research:
- Company name, city, industry information
- Job posting statistics (unique positions, total jobs)
- Outsourcing potential score
- Research status tracking

### linkedin_profiles
Stores found LinkedIn profiles:
- Profile information (name, title, URL, location)
- Relevance scoring and contact priority
- Search query used to find the profile
- Company association

### search_queries
Tracks search performance for optimization:
- Query text and results
- Success rates and execution times
- Error tracking

## Configuration

Edit `test_config.yaml` to customize:

### Company Extraction
- `max_companies`: Number of companies to extract (default: 10)
- `min_unique_positions`: Minimum job positions required (default: 3)
- `exclude_companies`: Keywords to exclude (recruitment agencies, etc.)
- `priority_industries`: Industries to prioritize for outsourcing

### LinkedIn Search
- `target_roles`: Roles to search for (CTO, VP Engineering, etc.)
- `search_templates`: Google search query templates
- `max_profiles_per_company`: Limit profiles per company (default: 5)
- `min_relevance_score`: Minimum score to save profiles (default: 60)

### Scoring System
- Role match weight: 40%
- Company match weight: 30%
- Location match weight: 20%
- Profile completeness weight: 10%

## Usage

### Run Complete Test
```bash
cd linkedin_test
python run_test.py
```

### Run Individual Steps
```bash
# Extract companies only
python run_test.py --step extract

# Find LinkedIn profiles only (requires companies to be extracted first)
python run_test.py --step linkedin

# Generate reports only (requires previous steps completed)
python run_test.py --step report
```

### Run Individual Components
```bash
# Company extraction
python company_extractor.py

# LinkedIn profile finding
python linkedin_finder.py

# Report generation
python report_generator.py
```

## Output Files

### Reports
- `linkedin_report_YYYYMMDD_HHMMSS.txt` - Detailed text report
- `linkedin_prospects_YYYYMMDD_HHMMSS.csv` - Company data in CSV format
- `linkedin_prospects_YYYYMMDD_HHMMSS_profiles.csv` - Profile data in CSV format

### Logs
- `company_extraction.log` - Company extraction process
- `linkedin_search.log` - LinkedIn search activities
- `test_run.log` - Overall test execution

## Search Strategy

The system uses multiple search strategies to find relevant LinkedIn profiles:

1. **Role-based searches**: Searches for specific roles (CTO, VP Engineering, etc.)
2. **Company-specific searches**: Combines company name with location and roles
3. **LinkedIn site searches**: Uses `site:linkedin.com` to focus on LinkedIn
4. **Relevance scoring**: Scores profiles based on role match, company match, location, etc.

## Contact Priority Classification

Profiles are classified into three priority levels:

- **High Priority (80+ score)**: Perfect role and company match
- **Medium Priority (60-79 score)**: Good role match or company association
- **Low Priority (< 60 score)**: Potential but lower relevance

## Limitations and Considerations

### Search Engine Limitations
- Uses web scraping of Google search results
- Subject to rate limiting and anti-bot measures
- For production use, consider Google Custom Search API

### LinkedIn Restrictions
- LinkedIn has strong anti-scraping measures
- Profile information extraction is limited
- Respects robots.txt and rate limits

### Data Quality
- Profile relevance depends on search query quality
- Some profiles may be outdated or incomplete
- Manual verification recommended for high-priority contacts

## Recommendations for Production Use

1. **API Integration**: Use Google Custom Search API for better reliability
2. **Enhanced Profile Extraction**: Integrate with LinkedIn Sales Navigator API
3. **Data Enrichment**: Add company size, revenue, and industry data
4. **CRM Integration**: Export directly to sales CRM systems
5. **Automated Follow-up**: Schedule and track outreach campaigns

## Troubleshooting

### Common Issues

1. **No companies found**
   - Check if HubSimplified has recent data
   - Adjust `min_unique_positions` in config
   - Verify database connection

2. **LinkedIn search fails**
   - Check internet connection
   - Reduce search frequency (increase delays)
   - Verify search query templates

3. **Low profile relevance scores**
   - Adjust scoring weights in config
   - Refine target roles list
   - Improve search query templates

### Debug Mode
Add logging level to see detailed execution:
```python
logging.getLogger().setLevel(logging.DEBUG)
```

## Future Enhancements

- AI-powered profile relevance scoring
- Automated email finding and verification
- Integration with sales automation tools
- Real-time company growth tracking
- Competitive analysis features

## Support

For issues or questions:
1. Check log files for error details
2. Verify configuration settings
3. Test individual components separately
4. Review database table contents for data issues
