#!/usr/bin/env python3
"""
LinkedIn Test Runner
Main script to run the complete LinkedIn profile extraction test
"""

import sys
import os
import logging
import argparse
from datetime import datetime

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from company_extractor import CompanyExtractor
from linkedin_finder import LinkedInFinder
from report_generator import ReportGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("linkedin_test/test_run.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class LinkedInTestRunner:
    def __init__(self):
        """Initialize the test runner"""
        self.extractor = None
        self.finder = None
        self.reporter = None
    
    def run_company_extraction(self) -> bool:
        """Step 1: Extract companies from HubSimplified data"""
        try:
            print("🏢 Step 1: Extracting companies from HubSimplified data...")
            
            self.extractor = CompanyExtractor()
            result = self.extractor.run_extraction()
            
            if result['success']:
                print(f"   ✅ Successfully extracted {result['companies_saved']} companies")
                print(f"   📊 Found {result['companies_found']} total companies")
                print(f"   🔍 {result['companies_filtered']} companies passed filtering")
                return True
            else:
                print(f"   ❌ Extraction failed: {result.get('message', result.get('error', 'Unknown error'))}")
                return False
                
        except Exception as e:
            print(f"   ❌ Critical error in company extraction: {e}")
            logging.error(f"Critical error in company extraction: {e}")
            return False
    
    def run_linkedin_research(self) -> bool:
        """Step 2: Find LinkedIn profiles for extracted companies"""
        try:
            print("\n🔍 Step 2: Searching for LinkedIn profiles...")
            
            self.finder = LinkedInFinder()
            result = self.finder.run_linkedin_research()
            
            if result['success']:
                if 'message' in result:
                    print(f"   ℹ️  {result['message']}")
                    return True
                else:
                    print(f"   ✅ LinkedIn research completed successfully!")
                    print(f"   🏢 Companies researched: {result['companies_researched']}")
                    print(f"   👥 Total profiles saved: {result['total_profiles_saved']}")
                    
                    # Show results by company
                    for company_result in result['results']:
                        if company_result['success']:
                            print(f"      • {company_result['company_name']}: {company_result['profiles_saved']} profiles")
                        else:
                            print(f"      • {company_result['company_name']}: Failed")
                    return True
            else:
                print(f"   ❌ LinkedIn research failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"   ❌ Critical error in LinkedIn research: {e}")
            logging.error(f"Critical error in LinkedIn research: {e}")
            return False
    
    def run_report_generation(self) -> bool:
        """Step 3: Generate reports with results"""
        try:
            print("\n📊 Step 3: Generating reports...")
            
            self.reporter = ReportGenerator()
            
            # Generate summary statistics
            stats = self.reporter.generate_summary_stats()
            
            if not stats:
                print("   ❌ No data found for report generation")
                return False
            
            # Print summary
            print(f"   📈 Summary Statistics:")
            print(f"      Companies: {stats['total_companies']}")
            print(f"      Research Completed: {stats['completed_research']}")
            print(f"      Total Profiles: {stats['total_profiles']}")
            print(f"      High Priority Contacts: {stats['high_priority_profiles']}")
            
            # Generate text report
            report = self.reporter.generate_text_report()
            
            # Save report to file
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"linkedin_test/linkedin_report_{timestamp}.txt"
            
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"   📄 Text report saved: {report_filename}")
            
            # Export to CSV
            csv_filename = self.reporter.export_to_csv()
            if csv_filename:
                print(f"   📊 CSV data exported: {csv_filename}")
            
            # Show top companies
            if stats['top_companies']:
                print(f"   🏆 Top Companies by Outsourcing Potential:")
                for i, company in enumerate(stats['top_companies'][:3], 1):
                    print(f"      {i}. {company['name']} ({company['city']}) - Score: {company['score']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Critical error in report generation: {e}")
            logging.error(f"Critical error in report generation: {e}")
            return False
    
    def run_full_test(self) -> bool:
        """Run the complete test workflow"""
        try:
            print("🚀 Starting LinkedIn Outsourcing Prospects Test")
            print("=" * 60)
            print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # Step 1: Extract companies
            if not self.run_company_extraction():
                print("\n❌ Test failed at company extraction step")
                return False
            
            # Step 2: Find LinkedIn profiles
            if not self.run_linkedin_research():
                print("\n❌ Test failed at LinkedIn research step")
                return False
            
            # Step 3: Generate reports
            if not self.run_report_generation():
                print("\n❌ Test failed at report generation step")
                return False
            
            print("\n" + "=" * 60)
            print("✅ LinkedIn Outsourcing Prospects Test Completed Successfully!")
            print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("\n📁 Check the linkedin_test/ folder for:")
            print("   • Text reports (linkedin_report_*.txt)")
            print("   • CSV exports (linkedin_prospects_*.csv)")
            print("   • Log files (*.log)")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Critical error in test workflow: {e}")
            logging.error(f"Critical error in test workflow: {e}")
            return False

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="LinkedIn Outsourcing Prospects Test",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_test.py                    # Run complete test workflow
  python run_test.py --step extract     # Run only company extraction
  python run_test.py --step linkedin    # Run only LinkedIn research
  python run_test.py --step report      # Run only report generation
        """
    )
    
    parser.add_argument('--step', choices=['extract', 'linkedin', 'report', 'all'],
                       default='all', help='Which step to run (default: all)')
    
    args = parser.parse_args()
    
    runner = LinkedInTestRunner()
    
    try:
        if args.step == 'extract':
            success = runner.run_company_extraction()
        elif args.step == 'linkedin':
            success = runner.run_linkedin_research()
        elif args.step == 'report':
            success = runner.run_report_generation()
        else:  # 'all'
            success = runner.run_full_test()
        
        if success:
            print("\n🎉 Operation completed successfully!")
            sys.exit(0)
        else:
            print("\n💥 Operation failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        logging.error(f"Unexpected error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
