#!/usr/bin/env python3
"""
Setup Script for LinkedIn Test
Initializes the test environment and verifies prerequisites
"""

import sys
import os
import logging
import yaml

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from heroku_config import load_heroku_config

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")

    issues = []
    config = None

    # Check if main config is available
    try:
        config = load_heroku_config()
        print("   ✅ Main configuration loaded")
    except Exception as e:
        issues.append(f"Main configuration error: {e}")
        return issues  # Can't continue without config

    # Check database connection and HubSimplified data in one connection
    try:
        db_manager = DatabaseManager(config)
        cursor = db_manager.connection.cursor()

        # Test basic connection
        cursor.execute("SELECT 1")
        cursor.fetchall()  # Clear any unread results
        print("   ✅ Database connection successful")

        # Check if HubSimplified data exists
        cursor.execute("SELECT COUNT(*) FROM jobs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
        recent_jobs = cursor.fetchone()[0]
        cursor.fetchall()  # Clear any unread results

        if recent_jobs > 0:
            print(f"   ✅ Found {recent_jobs} recent jobs in database")
        else:
            issues.append("No recent jobs found in database (last 7 days)")

        cursor.close()
        db_manager.close()

    except Exception as e:
        issues.append(f"Database connection error: {e}")

    # Check test config file - try both possible paths
    test_config_found = False
    for config_path in ['test_config.yaml', 'linkedin_test/test_config.yaml']:
        try:
            with open(config_path, 'r') as f:
                test_config = yaml.safe_load(f)
            print(f"   ✅ Test configuration file found at {config_path}")
            test_config_found = True
            break
        except FileNotFoundError:
            continue
        except Exception as e:
            issues.append(f"Test configuration error: {e}")
            break

    if not test_config_found:
        issues.append("Test configuration file not found")

    # Check required Python packages
    required_packages = ['requests', 'beautifulsoup4', 'mysql-connector-python', 'pyyaml']
    missing_packages = []

    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
            elif package == 'mysql-connector-python':
                import mysql.connector
            elif package == 'pyyaml':
                import yaml
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        issues.append(f"Missing Python packages: {', '.join(missing_packages)}")
    else:
        print("   ✅ Required Python packages available")

    return issues

def setup_database_tables():
    """Create test database tables"""
    print("\n🗄️  Setting up database tables...")

    try:
        config = load_heroku_config()
        db_manager = DatabaseManager(config)
        cursor = db_manager.connection.cursor()

        # Read and execute schema - try both possible paths
        schema_sql = None
        for schema_path in ['test_schema.sql', 'linkedin_test/test_schema.sql']:
            try:
                with open(schema_path, 'r') as schema_file:
                    schema_sql = schema_file.read()
                break
            except FileNotFoundError:
                continue

        if not schema_sql:
            raise FileNotFoundError("test_schema.sql not found")

        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        for statement in statements:
            cursor.execute(statement)

        db_manager.connection.commit()
        cursor.close()
        db_manager.close()

        print("   ✅ Test tables created successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error setting up database tables: {e}")
        return False

def verify_test_environment():
    """Verify the test environment is ready"""
    print("\n🧪 Verifying test environment...")
    
    try:
        config = load_heroku_config()
        db_manager = DatabaseManager(config)
        cursor = db_manager.connection.cursor()
        
        # Check if test tables exist
        tables_to_check = ['test_companies', 'linkedin_profiles', 'search_queries']
        existing_tables = []
        
        for table in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                existing_tables.append(table)
        
        cursor.close()
        db_manager.close()
        
        if len(existing_tables) == len(tables_to_check):
            print(f"   ✅ All test tables exist: {', '.join(existing_tables)}")
            return True
        else:
            missing = set(tables_to_check) - set(existing_tables)
            print(f"   ❌ Missing tables: {', '.join(missing)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error verifying test environment: {e}")
        return False

def show_next_steps():
    """Show next steps to run the test"""
    print("\n📋 Next Steps:")
    print("   1. Review and customize test_config.yaml if needed")
    print("   2. Run the complete test:")
    print("      python linkedin_test/run_test.py")
    print("   3. Or run individual steps:")
    print("      python linkedin_test/run_test.py --step extract")
    print("      python linkedin_test/run_test.py --step linkedin")
    print("      python linkedin_test/run_test.py --step report")
    print("\n📁 Output files will be saved in the linkedin_test/ folder")

def main():
    """Main setup function"""
    print("🚀 LinkedIn Test Setup")
    print("=" * 50)
    
    # Check prerequisites
    issues = check_prerequisites()
    
    if issues:
        print("\n❌ Prerequisites check failed:")
        for issue in issues:
            print(f"   • {issue}")
        print("\nPlease resolve these issues before running the test.")
        return False
    
    print("\n✅ All prerequisites met!")
    
    # Setup database tables
    if not setup_database_tables():
        print("\n❌ Database setup failed!")
        return False
    
    # Verify test environment
    if not verify_test_environment():
        print("\n❌ Test environment verification failed!")
        return False
    
    print("\n✅ Test environment setup completed successfully!")
    
    # Show next steps
    show_next_steps()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Setup completed! You're ready to run the LinkedIn test.")
            sys.exit(0)
        else:
            print("\n💥 Setup failed! Please check the errors above.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during setup: {e}")
        sys.exit(1)
